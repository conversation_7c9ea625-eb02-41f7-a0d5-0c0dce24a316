<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صورة البروفايل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .profile-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .profile-image {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            object-fit: cover;
            object-position: center;
            border: 4px solid #f59e0b;
            margin: 20px 0;
        }
        .test-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        .test-image {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <h1>اختبار صورة البروفايل الجديدة</h1>
        
        <h2>الصورة الجديدة:</h2>
        <img src="/new-profile-photo.jpg" alt="الصورة الجديدة" class="profile-image" 
             onerror="this.style.border='4px solid red'; this.alt='فشل تحميل الصورة الجديدة';">
        
        <h2>الصورة القديمة للمقارنة:</h2>
        <img src="/profile-photo.jpg" alt="الصورة القديمة" class="profile-image"
             onerror="this.style.border='4px solid red'; this.alt='فشل تحميل الصورة القديمة';">
        
        <div class="test-images">
            <div>
                <h3>الصورة الأصلية القديمة</h3>
                <img src="/lovable-uploads/1b7275d0-9db2-4ece-951a-c68c19378349.png" 
                     alt="الصورة الأصلية" class="test-image"
                     onerror="this.style.border='2px solid red'; this.alt='فشل تحميل الصورة الأصلية';">
            </div>
        </div>
        
        <p>إذا ظهرت الصورة الجديدة بشكل صحيح، فهذا يعني أن المسار يعمل بشكل جيد.</p>
    </div>
</body>
</html>
